import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  Paper,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  DatePicker,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  LinearProgress,
  CircularProgress,
  Alert,
  Tabs,
  Tab
} from '@mui/material';
import {
  TrendingUp,
  People,
  School,
  WorkspacePremium,
  Download,
  Refresh,
  BarChart,
  PieChart,
  Timeline,
  Assessment,
  Group,
  VideoLibrary,
  Star,
  CheckCircle
} from '@mui/icons-material';
import { analyticsService } from '../../firebase/adminServices';
import toast from 'react-hot-toast';

const AnalyticsReports = () => {
  const [loading, setLoading] = useState(false);
  const [tabValue, setTabValue] = useState(0);
  const [dateRange, setDateRange] = useState('30');
  const [reportType, setReportType] = useState('overview');

  const [analytics, setAnalytics] = useState({
    overview: {
      totalStudents: 156,
      activeStudents: 89,
      totalCourses: 12,
      completedCourses: 234,
      totalCertificates: 89,
      averageProgress: 67
    },
    studentStats: {
      newRegistrations: 23,
      activeThisWeek: 67,
      completionRate: 78,
      averageTimeSpent: 45
    },
    courseStats: {
      mostPopular: 'أساسيات التسويق الرقمي',
      highestCompletion: 'إدارة وسائل التواصل',
      averageRating: 4.7,
      totalViews: 1234
    },
    recentActivity: [
      {
        id: 1,
        student: 'أحمد محمد',
        action: 'أكمل كورس التسويق الرقمي',
        timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString(),
        type: 'completion'
      },
      {
        id: 2,
        student: 'فاطمة أحمد',
        action: 'بدأ كورس إدارة المشاريع',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(),
        type: 'enrollment'
      },
      {
        id: 3,
        student: 'محمد علي',
        action: 'حصل على شهادة إتمام',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 5).toISOString(),
        type: 'certificate'
      }
    ]
  });

  useEffect(() => {
    loadAnalytics();
  }, [dateRange, reportType]);

  const loadAnalytics = async () => {
    try {
      setLoading(true);

      // جلب الإحصائيات الحقيقية من Firebase
      const overviewStats = await analyticsService.getOverviewStats();
      const recentActivity = await analyticsService.getRecentActivity(5);

      setAnalytics(prev => ({
        ...prev,
        overview: overviewStats.overview,
        studentStats: overviewStats.students,
        courseStats: overviewStats.courses,
        recentActivity: recentActivity
      }));

    } catch (error) {
      console.error('Error loading analytics:', error);
      toast.error('فشل في تحميل التقارير');
    } finally {
      setLoading(false);
    }
  };

  const exportReport = async () => {
    try {
      setLoading(true);
      // Simulate export functionality
      await new Promise(resolve => setTimeout(resolve, 2000));
      toast.success('تم تصدير التقرير بنجاح');
    } catch (error) {
      console.error('Error exporting report:', error);
      toast.error('فشل في تصدير التقرير');
    } finally {
      setLoading(false);
    }
  };

  const formatTime = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now - date) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      return 'منذ قليل';
    } else if (diffInHours < 24) {
      return `منذ ${Math.floor(diffInHours)} ساعة`;
    } else {
      return date.toLocaleDateString('ar-SA');
    }
  };

  const getActivityIcon = (type) => {
    switch (type) {
      case 'completion':
        return <CheckCircle color="success" />;
      case 'enrollment':
        return <School color="primary" />;
      case 'certificate':
        return <WorkspacePremium color="warning" />;
      default:
        return <Star color="info" />;
    }
  };

  const OverviewTab = () => (
    <Grid container spacing={3}>
      {/* Key Metrics Cards */}
      <Grid item xs={12} sm={6} md={3}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Box>
                <Typography color="text.secondary" gutterBottom>
                  إجمالي الطلاب
                </Typography>
                <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                  {analytics.overview.totalStudents}
                </Typography>
                <Typography variant="body2" color="success.main">
                  +12% من الشهر الماضي
                </Typography>
              </Box>
              <People sx={{ fontSize: 40, color: '#2196F3' }} />
            </Box>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} sm={6} md={3}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Box>
                <Typography color="text.secondary" gutterBottom>
                  الطلاب النشطون
                </Typography>
                <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                  {analytics.overview.activeStudents}
                </Typography>
                <Typography variant="body2" color="success.main">
                  +8% من الأسبوع الماضي
                </Typography>
              </Box>
              <Group sx={{ fontSize: 40, color: '#4CAF50' }} />
            </Box>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} sm={6} md={3}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Box>
                <Typography color="text.secondary" gutterBottom>
                  إجمالي الكورسات
                </Typography>
                <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                  {analytics.overview.totalCourses}
                </Typography>
                <Typography variant="body2" color="info.main">
                  +2 كورس جديد
                </Typography>
              </Box>
              <VideoLibrary sx={{ fontSize: 40, color: '#FF9800' }} />
            </Box>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} sm={6} md={3}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Box>
                <Typography color="text.secondary" gutterBottom>
                  الشهادات الصادرة
                </Typography>
                <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                  {analytics.overview.totalCertificates}
                </Typography>
                <Typography variant="body2" color="success.main">
                  +15% هذا الشهر
                </Typography>
              </Box>
              <WorkspacePremium sx={{ fontSize: 40, color: '#9C27B0' }} />
            </Box>
          </CardContent>
        </Card>
      </Grid>

      {/* Progress Chart */}
      <Grid item xs={12} md={8}>
        <Card>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 3 }}>
              معدل التقدم العام
            </Typography>
            <Box sx={{ mb: 2 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2">التقدم الإجمالي</Typography>
                <Typography variant="body2">{analytics.overview.averageProgress}%</Typography>
              </Box>
              <LinearProgress
                variant="determinate"
                value={analytics.overview.averageProgress}
                sx={{ height: 8, borderRadius: 4 }}
              />
            </Box>

            <Grid container spacing={2}>
              <Grid item xs={6}>
                <Typography variant="body2" color="text.secondary">
                  الكورسات المكتملة
                </Typography>
                <Typography variant="h6">
                  {analytics.overview.completedCourses}
                </Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body2" color="text.secondary">
                  معدل الإكمال
                </Typography>
                <Typography variant="h6">
                  78%
                </Typography>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Grid>

      {/* Recent Activity */}
      <Grid item xs={12} md={4}>
        <Card>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 3 }}>
              النشاط الأخير
            </Typography>
            <Box>
              {analytics.recentActivity.map((activity) => (
                <Box key={activity.id} sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  {getActivityIcon(activity.type)}
                  <Box sx={{ ml: 2, flexGrow: 1 }}>
                    <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                      {activity.student}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {activity.action}
                    </Typography>
                    <Typography variant="caption" display="block" color="text.secondary">
                      {formatTime(activity.timestamp)}
                    </Typography>
                  </Box>
                </Box>
              ))}
            </Box>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  const StudentsTab = () => (
    <Grid container spacing={3}>
      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 3 }}>
              إحصائيات الطلاب
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <Box sx={{ textAlign: 'center', p: 2 }}>
                  <Typography variant="h4" color="primary.main">
                    {analytics.studentStats.newRegistrations}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    تسجيلات جديدة
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={6}>
                <Box sx={{ textAlign: 'center', p: 2 }}>
                  <Typography variant="h4" color="success.main">
                    {analytics.studentStats.activeThisWeek}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    نشط هذا الأسبوع
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={6}>
                <Box sx={{ textAlign: 'center', p: 2 }}>
                  <Typography variant="h4" color="warning.main">
                    {analytics.studentStats.completionRate}%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    معدل الإكمال
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={6}>
                <Box sx={{ textAlign: 'center', p: 2 }}>
                  <Typography variant="h4" color="info.main">
                    {analytics.studentStats.averageTimeSpent}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    دقيقة متوسط الوقت
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 3 }}>
              أداء الطلاب
            </Typography>
            <Alert severity="info" sx={{ mb: 2 }}>
              الرسوم البيانية التفاعلية ستكون متاحة قريباً
            </Alert>
            <Box sx={{ height: 200, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              <BarChart sx={{ fontSize: 64, color: 'text.secondary' }} />
            </Box>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  const CoursesTab = () => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 3 }}>
              إحصائيات الكورسات
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} md={3}>
                <Box sx={{ textAlign: 'center', p: 2 }}>
                  <Typography variant="h6" color="text.secondary">
                    الأكثر شعبية
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 'bold', mt: 1 }}>
                    {analytics.courseStats.mostPopular}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} md={3}>
                <Box sx={{ textAlign: 'center', p: 2 }}>
                  <Typography variant="h6" color="text.secondary">
                    أعلى معدل إكمال
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 'bold', mt: 1 }}>
                    {analytics.courseStats.highestCompletion}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} md={3}>
                <Box sx={{ textAlign: 'center', p: 2 }}>
                  <Typography variant="h6" color="text.secondary">
                    متوسط التقييم
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 'bold', mt: 1 }}>
                    {analytics.courseStats.averageRating} ⭐
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} md={3}>
                <Box sx={{ textAlign: 'center', p: 2 }}>
                  <Typography variant="h6" color="text.secondary">
                    إجمالي المشاهدات
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 'bold', mt: 1 }}>
                    {analytics.courseStats.totalViews.toLocaleString()}
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
          التقارير والإحصائيات
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>الفترة الزمنية</InputLabel>
            <Select
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value)}
              label="الفترة الزمنية"
            >
              <MenuItem value="7">آخر 7 أيام</MenuItem>
              <MenuItem value="30">آخر 30 يوم</MenuItem>
              <MenuItem value="90">آخر 3 أشهر</MenuItem>
              <MenuItem value="365">آخر سنة</MenuItem>
            </Select>
          </FormControl>

          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={loadAnalytics}
            disabled={loading}
          >
            تحديث
          </Button>

          <Button
            variant="contained"
            startIcon={<Download />}
            onClick={exportReport}
            disabled={loading}
            sx={{ bgcolor: '#0000FF' }}
          >
            تصدير التقرير
          </Button>
        </Box>
      </Box>

      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={tabValue}
          onChange={(e, newValue) => setTabValue(newValue)}
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          <Tab label="نظرة عامة" icon={<Assessment />} />
          <Tab label="الطلاب" icon={<People />} />
          <Tab label="الكورسات" icon={<School />} />
        </Tabs>
      </Paper>

      {loading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
          <CircularProgress />
        </Box>
      )}

      <Box sx={{ mt: 3 }}>
        {tabValue === 0 && <OverviewTab />}
        {tabValue === 1 && <StudentsTab />}
        {tabValue === 2 && <CoursesTab />}
      </Box>
    </Box>
  );
};

export default AnalyticsReports;