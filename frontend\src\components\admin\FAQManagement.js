import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Grid,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  IconButton,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Fab
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  ExpandMore,
  Help,
  Save,
  Cancel,
  Search,
  Category,
  Visibility,
  VisibilityOff
} from '@mui/icons-material';
import { faqManagementService } from '../../firebase/adminServices';
import toast from 'react-hot-toast';
import realtimeService from '../../firebase/realtimeService';

const FAQManagement = () => {
  const [faqs, setFaqs] = useState([]);
  const [loading, setLoading] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [editingFaq, setEditingFaq] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');

  const [faqData, setFaqData] = useState({
    question: '',
    answer: '',
    category: '',
    priority: 1,
    isActive: true
  });

  const categories = [
    'عام',
    'التسجيل والحساب',
    'الكورسات والمحتوى',
    'الشهادات',
    'المدفوعات',
    'الدعم التقني'
  ];

  useEffect(() => {
    loadFAQs();
  }, []);

  const loadFAQs = async () => {
    try {
      setLoading(true);
      const faqsData = await faqManagementService.getAllFAQs();
      setFaqs(faqsData);
    } catch (error) {
      console.error('Error loading FAQs:', error);
      toast.error('فشل في تحميل الأسئلة الشائعة');
    } finally {
      setLoading(false);
    }
  };

  const handleOpenDialog = (faq = null) => {
    if (faq) {
      setEditingFaq(faq);
      setFaqData({
        question: faq.question,
        answer: faq.answer,
        category: faq.category,
        priority: faq.priority,
        isActive: faq.isActive
      });
    } else {
      setEditingFaq(null);
      setFaqData({
        question: '',
        answer: '',
        category: '',
        priority: 1,
        isActive: true
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setEditingFaq(null);
    setFaqData({
      question: '',
      answer: '',
      category: '',
      priority: 1,
      isActive: true
    });
  };

  const handleSaveFAQ = async () => {
    if (!faqData.question.trim() || !faqData.answer.trim() || !faqData.category) {
      toast.error('يرجى ملء جميع الحقول المطلوبة');
      return;
    }

    try {
      setLoading(true);

      if (editingFaq) {
        // Update existing FAQ
        await faqManagementService.updateFAQ(editingFaq.id, faqData);
        setFaqs(faqs.map(faq =>
          faq.id === editingFaq.id
            ? { ...faq, ...faqData }
            : faq
        ));
        toast.success('تم تحديث السؤال بنجاح');
      } else {
        // Add new FAQ مع تحديث فوري
        const result = await realtimeService.addFAQ(faqData);
        if (result.success) {
          toast.success('تم إضافة السؤال بنجاح - سيظهر فوراً للطلاب');
        }
      }

      handleCloseDialog();
    } catch (error) {
      console.error('Error saving FAQ:', error);
      toast.error('فشل في حفظ السؤال');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteFAQ = async (faqId) => {
    if (!window.confirm('هل أنت متأكد من حذف هذا السؤال؟')) {
      return;
    }

    try {
      setLoading(true);
      await faqManagementService.deleteFAQ(faqId);
      setFaqs(faqs.filter(faq => faq.id !== faqId));
      toast.success('تم حذف السؤال بنجاح');
    } catch (error) {
      console.error('Error deleting FAQ:', error);
      toast.error('فشل في حذف السؤال');
    } finally {
      setLoading(false);
    }
  };

  const toggleFAQStatus = async (faqId) => {
    try {
      const faq = faqs.find(f => f.id === faqId);
      await faqManagementService.toggleFAQStatus(faqId, !faq.isActive);
      setFaqs(faqs.map(faq =>
        faq.id === faqId
          ? { ...faq, isActive: !faq.isActive }
          : faq
      ));
      toast.success('تم تحديث حالة السؤال');
    } catch (error) {
      console.error('Error toggling FAQ status:', error);
      toast.error('فشل في تحديث حالة السؤال');
    }
  };

  const filteredFAQs = faqs.filter(faq => {
    const matchesSearch = faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         faq.answer.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = !selectedCategory || faq.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
          إدارة الأسئلة الشائعة
        </Typography>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={() => handleOpenDialog()}
          sx={{ bgcolor: '#0000FF' }}
        >
          إضافة سؤال جديد
        </Button>
      </Box>

      {/* Search and Filter */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                placeholder="البحث في الأسئلة..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: <Search sx={{ mr: 1, color: 'text.secondary' }} />
                }}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel>تصنيف</InputLabel>
                <Select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  label="تصنيف"
                >
                  <MenuItem value="">جميع التصنيفات</MenuItem>
                  {categories.map(category => (
                    <MenuItem key={category} value={category}>
                      {category}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <Typography variant="body2" color="text.secondary">
                {filteredFAQs.length} سؤال
              </Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* FAQs List */}
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
          <CircularProgress />
        </Box>
      ) : filteredFAQs.length === 0 ? (
        <Alert severity="info">
          لا توجد أسئلة شائعة متاحة
        </Alert>
      ) : (
        <Box>
          {filteredFAQs
            .sort((a, b) => a.priority - b.priority)
            .map((faq) => (
              <Accordion key={faq.id} sx={{ mb: 2 }}>
                <AccordionSummary expandIcon={<ExpandMore />}>
                  <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', gap: 2 }}>
                    <Help sx={{ color: 'primary.main' }} />
                    <Typography sx={{ flexGrow: 1, fontWeight: 'medium' }}>
                      {faq.question}
                    </Typography>
                    <Chip
                      label={faq.category}
                      size="small"
                      color="primary"
                      variant="outlined"
                    />
                    <Chip
                      label={faq.isActive ? 'نشط' : 'غير نشط'}
                      size="small"
                      color={faq.isActive ? 'success' : 'default'}
                    />
                  </Box>
                </AccordionSummary>
                <AccordionDetails>
                  <Box>
                    <Typography variant="body1" sx={{ mb: 2 }}>
                      {faq.answer}
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Button
                        size="small"
                        startIcon={<Edit />}
                        onClick={() => handleOpenDialog(faq)}
                      >
                        تحرير
                      </Button>
                      <Button
                        size="small"
                        startIcon={faq.isActive ? <VisibilityOff /> : <Visibility />}
                        onClick={() => toggleFAQStatus(faq.id)}
                        color={faq.isActive ? 'warning' : 'success'}
                      >
                        {faq.isActive ? 'إخفاء' : 'إظهار'}
                      </Button>
                      <Button
                        size="small"
                        startIcon={<Delete />}
                        onClick={() => handleDeleteFAQ(faq.id)}
                        color="error"
                      >
                        حذف
                      </Button>
                    </Box>
                  </Box>
                </AccordionDetails>
              </Accordion>
            ))}
        </Box>
      )}

      {/* Add/Edit FAQ Dialog */}
      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {editingFaq ? 'تحرير السؤال' : 'إضافة سؤال جديد'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="السؤال *"
                  value={faqData.question}
                  onChange={(e) => setFaqData({ ...faqData, question: e.target.value })}
                  multiline
                  rows={2}
                />
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="الإجابة *"
                  value={faqData.answer}
                  onChange={(e) => setFaqData({ ...faqData, answer: e.target.value })}
                  multiline
                  rows={4}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>التصنيف *</InputLabel>
                  <Select
                    value={faqData.category}
                    onChange={(e) => setFaqData({ ...faqData, category: e.target.value })}
                    label="التصنيف *"
                  >
                    {categories.map(category => (
                      <MenuItem key={category} value={category}>
                        {category}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="الأولوية"
                  type="number"
                  value={faqData.priority}
                  onChange={(e) => setFaqData({ ...faqData, priority: parseInt(e.target.value) })}
                  inputProps={{ min: 1, max: 10 }}
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog} startIcon={<Cancel />}>
            إلغاء
          </Button>
          <Button
            onClick={handleSaveFAQ}
            variant="contained"
            startIcon={<Save />}
            disabled={loading}
          >
            {loading ? <CircularProgress size={20} /> : 'حفظ'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Floating Action Button */}
      <Fab
        color="primary"
        aria-label="add"
        onClick={() => handleOpenDialog()}
        sx={{
          position: 'fixed',
          bottom: 16,
          right: 16,
          bgcolor: '#0000FF'
        }}
      >
        <Add />
      </Fab>
    </Box>
  );
};

export default FAQManagement;