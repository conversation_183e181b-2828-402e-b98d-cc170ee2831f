import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  Grid,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  IconButton,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Switch,
  FormControlLabel,
  CircularProgress,
  Alert,
  Avatar,
  Tooltip
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  People,
  School,
  Save,
  Cancel,
  Refresh,
  PersonAdd,
  Code,
  CheckCircle,
  Cancel as CancelIcon
} from '@mui/icons-material';
import toast from 'react-hot-toast';

// استيراد خدمات قاعدة البيانات الحقيقية
import { 
  collection, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  doc, 
  getDocs, 
  onSnapshot,
  serverTimestamp,
  query,
  orderBy,
  where
} from 'firebase/firestore';
import { db } from '../../firebase/config';

const StudentManagement = () => {
  const [students, setStudents] = useState([]);
  const [loading, setLoading] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [editingStudent, setEditingStudent] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    studentCode: '',
    isActive: true,
    enrolledCourses: 0,
    completedCourses: 0,
    totalWatchTime: 0
  });

  // جلب الطلاب من قاعدة البيانات مع التحديث الفوري (بدون فهارس معقدة)
  useEffect(() => {
    console.log('🔄 بدء مراقبة الطلاب...');

    const unsubscribe = onSnapshot(
      query(collection(db, 'users'), where('role', '==', 'student')),
      (snapshot) => {
        const studentsData = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          createdAt: doc.data().createdAt?.toDate?.() || new Date(),
          updatedAt: doc.data().updatedAt?.toDate?.() || new Date()
        }));

        // ترتيب البيانات محلياً بدلاً من في الاستعلام
        studentsData.sort((a, b) => b.createdAt - a.createdAt);

        setStudents(studentsData);
        console.log('✅ تم تحديث الطلاب:', studentsData.length);
      },
      (error) => {
        console.error('❌ خطأ في جلب الطلاب:', error);
        toast.error('فشل في جلب الطلاب');
      }
    );

    return () => {
      console.log('🛑 إيقاف مراقبة الطلاب');
      unsubscribe();
    };
  }, []);

  // توليد كود طالب عشوائي
  const generateStudentCode = () => {
    const code = Math.floor(100000 + Math.random() * 900000).toString();
    // التحقق من عدم تكرار الكود
    const existingCodes = students.map(s => s.studentCode);
    if (existingCodes.includes(code)) {
      return generateStudentCode(); // إعادة المحاولة إذا كان الكود موجود
    }
    return code;
  };

  // إضافة طالب جديد
  const handleAddStudent = async () => {
    if (!formData.name.trim()) {
      toast.error('يرجى إدخال اسم الطالب');
      return;
    }

    // توليد كود طالب إذا لم يتم إدخاله
    const studentCode = formData.studentCode.trim() || generateStudentCode();
    
    // التحقق من عدم تكرار الكود
    const existingCodes = students.map(s => s.studentCode);
    if (existingCodes.includes(studentCode)) {
      toast.error('كود الطالب موجود مسبقاً');
      return;
    }

    setLoading(true);
    try {
      const studentData = {
        ...formData,
        studentCode,
        role: 'student',
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      };

      const docRef = await addDoc(collection(db, 'users'), studentData);
      
      console.log('✅ تم إضافة الطالب:', docRef.id);
      toast.success(`تم إضافة الطالب بنجاح - كود الطالب: ${studentCode}`);
      
      handleCloseDialog();
    } catch (error) {
      console.error('❌ خطأ في إضافة الطالب:', error);
      toast.error('فشل في إضافة الطالب');
    } finally {
      setLoading(false);
    }
  };

  // تحديث طالب موجود
  const handleUpdateStudent = async () => {
    if (!editingStudent || !formData.name.trim()) {
      toast.error('يرجى إدخال اسم الطالب');
      return;
    }

    // التحقق من عدم تكرار كود الطالب
    const existingCodes = students
      .filter(s => s.id !== editingStudent.id)
      .map(s => s.studentCode);
    
    if (existingCodes.includes(formData.studentCode)) {
      toast.error('كود الطالب موجود مسبقاً');
      return;
    }

    setLoading(true);
    try {
      const studentRef = doc(db, 'users', editingStudent.id);
      const updateData = {
        ...formData,
        updatedAt: serverTimestamp()
      };

      await updateDoc(studentRef, updateData);
      
      console.log('✅ تم تحديث الطالب:', editingStudent.id);
      toast.success('تم تحديث بيانات الطالب بنجاح');
      
      handleCloseDialog();
    } catch (error) {
      console.error('❌ خطأ في تحديث الطالب:', error);
      toast.error('فشل في تحديث الطالب');
    } finally {
      setLoading(false);
    }
  };

  // حذف طالب
  const handleDeleteStudent = async (studentId, studentName) => {
    if (!window.confirm(`هل أنت متأكد من حذف الطالب "${studentName}"؟`)) {
      return;
    }

    setLoading(true);
    try {
      await deleteDoc(doc(db, 'users', studentId));
      
      console.log('✅ تم حذف الطالب:', studentId);
      toast.success('تم حذف الطالب بنجاح');
    } catch (error) {
      console.error('❌ خطأ في حذف الطالب:', error);
      toast.error('فشل في حذف الطالب');
    } finally {
      setLoading(false);
    }
  };

  // تبديل حالة الطالب (مفعل/غير مفعل)
  const handleToggleStudentStatus = async (studentId, currentStatus) => {
    try {
      const studentRef = doc(db, 'users', studentId);
      await updateDoc(studentRef, {
        isActive: !currentStatus,
        updatedAt: serverTimestamp()
      });
      
      toast.success(`تم ${!currentStatus ? 'تفعيل' : 'إلغاء تفعيل'} الطالب`);
    } catch (error) {
      console.error('❌ خطأ في تغيير حالة الطالب:', error);
      toast.error('فشل في تغيير حالة الطالب');
    }
  };

  // فتح نافذة إضافة طالب جديد
  const handleOpenAddDialog = () => {
    setEditingStudent(null);
    setFormData({
      name: '',
      email: '',
      phone: '',
      studentCode: '',
      isActive: true,
      enrolledCourses: 0,
      completedCourses: 0,
      totalWatchTime: 0
    });
    setOpenDialog(true);
  };

  // فتح نافذة تعديل طالب
  const handleOpenEditDialog = (student) => {
    setEditingStudent(student);
    setFormData({
      name: student.name || '',
      email: student.email || '',
      phone: student.phone || '',
      studentCode: student.studentCode || '',
      isActive: student.isActive !== undefined ? student.isActive : true,
      enrolledCourses: student.enrolledCourses || 0,
      completedCourses: student.completedCourses || 0,
      totalWatchTime: student.totalWatchTime || 0
    });
    setOpenDialog(true);
  };

  // إغلاق النافذة
  const handleCloseDialog = () => {
    setOpenDialog(false);
    setEditingStudent(null);
    setFormData({
      name: '',
      email: '',
      phone: '',
      studentCode: '',
      isActive: true,
      enrolledCourses: 0,
      completedCourses: 0,
      totalWatchTime: 0
    });
  };

  // تحديث بيانات النموذج
  const handleFormChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // تنسيق التاريخ
  const formatDate = (date) => {
    if (!date) return 'غير محدد';
    if (date.toDate) date = date.toDate();
    return new Intl.DateTimeFormat('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  // الحصول على الأحرف الأولى من الاسم للأفاتار
  const getInitials = (name) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  return (
    <Box>
      {/* رأس الصفحة */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <People color="primary" />
          إدارة الطلاب
        </Typography>
        <Button
          variant="contained"
          startIcon={<PersonAdd />}
          onClick={handleOpenAddDialog}
          size="large"
          sx={{ borderRadius: 2 }}
        >
          إضافة طالب جديد
        </Button>
      </Box>

      {/* إحصائيات سريعة */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ textAlign: 'center', p: 2 }}>
            <Typography variant="h4" color="primary">{students.length}</Typography>
            <Typography variant="body2" color="text.secondary">إجمالي الطلاب</Typography>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ textAlign: 'center', p: 2 }}>
            <Typography variant="h4" color="success.main">
              {students.filter(s => s.isActive).length}
            </Typography>
            <Typography variant="body2" color="text.secondary">الطلاب المفعلين</Typography>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ textAlign: 'center', p: 2 }}>
            <Typography variant="h4" color="info.main">
              {students.reduce((total, student) => total + (student.enrolledCourses || 0), 0)}
            </Typography>
            <Typography variant="body2" color="text.secondary">إجمالي التسجيلات</Typography>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ textAlign: 'center', p: 2 }}>
            <Typography variant="h4" color="warning.main">
              {students.reduce((total, student) => total + (student.completedCourses || 0), 0)}
            </Typography>
            <Typography variant="body2" color="text.secondary">الكورسات المكتملة</Typography>
          </Card>
        </Grid>
      </Grid>

      {/* جدول الطلاب */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            قائمة الطلاب ({students.length})
          </Typography>
          
          {loading && (
            <Box display="flex" justifyContent="center" p={3}>
              <CircularProgress />
            </Box>
          )}

          {!loading && students.length === 0 && (
            <Alert severity="info" sx={{ mt: 2 }}>
              لا يوجد طلاب حالياً. اضغط على "إضافة طالب جديد" لإنشاء أول طالب.
            </Alert>
          )}

          {!loading && students.length > 0 && (
            <TableContainer component={Paper} sx={{ mt: 2 }}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>الطالب</TableCell>
                    <TableCell>كود الطالب</TableCell>
                    <TableCell>البريد الإلكتروني</TableCell>
                    <TableCell>الهاتف</TableCell>
                    <TableCell>الكورسات</TableCell>
                    <TableCell>المكتمل</TableCell>
                    <TableCell>الحالة</TableCell>
                    <TableCell>تاريخ التسجيل</TableCell>
                    <TableCell>الإجراءات</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {students.map((student) => (
                    <TableRow key={student.id}>
                      <TableCell>
                        <Box display="flex" alignItems="center" gap={2}>
                          <Avatar sx={{ bgcolor: 'primary.main' }}>
                            {getInitials(student.name)}
                          </Avatar>
                          <Box>
                            <Typography variant="subtitle2" fontWeight="bold">
                              {student.name}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              ID: {student.id.substring(0, 8)}...
                            </Typography>
                          </Box>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip 
                          icon={<Code />}
                          label={student.studentCode}
                          size="small"
                          color="primary"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>{student.email || 'غير محدد'}</TableCell>
                      <TableCell>{student.phone || 'غير محدد'}</TableCell>
                      <TableCell>
                        <Chip 
                          icon={<School />}
                          label={student.enrolledCourses || 0}
                          size="small"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <Chip 
                          icon={<CheckCircle />}
                          label={student.completedCourses || 0}
                          size="small"
                          color="success"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <FormControlLabel
                          control={
                            <Switch
                              checked={student.isActive}
                              onChange={() => handleToggleStudentStatus(student.id, student.isActive)}
                              size="small"
                            />
                          }
                          label={student.isActive ? 'مفعل' : 'غير مفعل'}
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="caption">
                          {formatDate(student.createdAt)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Box display="flex" gap={1}>
                          <Tooltip title="تعديل">
                            <IconButton
                              size="small"
                              onClick={() => handleOpenEditDialog(student)}
                              color="primary"
                            >
                              <Edit />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="حذف">
                            <IconButton
                              size="small"
                              onClick={() => handleDeleteStudent(student.id, student.name)}
                              color="error"
                            >
                              <Delete />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </CardContent>
      </Card>

      {/* نافذة إضافة/تعديل الطالب */}
      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
        aria-labelledby="student-dialog-title"
        aria-describedby="student-dialog-description"
      >
        <DialogTitle id="student-dialog-title">
          {editingStudent ? 'تعديل بيانات الطالب' : 'إضافة طالب جديد'}
        </DialogTitle>
        <DialogContent id="student-dialog-description">
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="اسم الطالب *"
                value={formData.name}
                onChange={(e) => handleFormChange('name', e.target.value)}
                variant="outlined"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="كود الطالب"
                value={formData.studentCode}
                onChange={(e) => handleFormChange('studentCode', e.target.value)}
                variant="outlined"
                placeholder="سيتم توليده تلقائياً إذا ترك فارغاً"
                helperText="6 أرقام فريدة للطالب"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="البريد الإلكتروني"
                type="email"
                value={formData.email}
                onChange={(e) => handleFormChange('email', e.target.value)}
                variant="outlined"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="رقم الهاتف"
                value={formData.phone}
                onChange={(e) => handleFormChange('phone', e.target.value)}
                variant="outlined"
                placeholder="05xxxxxxxx"
              />
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.isActive}
                    onChange={(e) => handleFormChange('isActive', e.target.checked)}
                  />
                }
                label="حساب مفعل"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog} startIcon={<Cancel />}>
            إلغاء
          </Button>
          <Button
            onClick={editingStudent ? handleUpdateStudent : handleAddStudent}
            variant="contained"
            startIcon={<Save />}
            disabled={loading}
          >
            {loading ? <CircularProgress size={20} /> : (editingStudent ? 'تحديث' : 'إضافة')}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default StudentManagement;
