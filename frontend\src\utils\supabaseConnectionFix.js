/**
 * إصلاح اتصال Supabase - SKILLS WORLD ACADEMY
 * Supabase Connection Fix & Diagnostics
 */

import { createClient } from '@supabase/supabase-js';

// إعدادات Supabase المباشرة (مضمونة العمل)
const SUPABASE_URL = 'https://auwpeiicfwcysoexoogf.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImF1d3BlaWljZndjeXNvZXhvb2dmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIxNzA3MzAsImV4cCI6MjA2Nzc0NjczMH0.3twZ1c6M2EyBoBe66PjmwsUwvzK0nnV89Bt3yLcGBjY';

// إنشاء عميل Supabase مبسط
const supabaseClient = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
  auth: {
    persistSession: false,
    autoRefreshToken: false
  }
});

/**
 * اختبار اتصال مباشر مع Supabase
 */
export const testDirectConnection = async () => {
  try {
    console.log('🔄 اختبار اتصال مباشر مع Supabase...');
    console.log('📡 URL:', SUPABASE_URL);
    
    // اختبار بسيط جداً
    const { data, error } = await supabaseClient
      .from('settings')
      .select('key, value')
      .limit(1);

    if (error) {
      console.error('❌ خطأ في الاتصال:', error);
      return {
        success: false,
        error: error.message,
        details: error
      };
    }

    console.log('✅ نجح الاتصال مع Supabase!');
    console.log('📊 البيانات المستلمة:', data);
    
    return {
      success: true,
      data: data,
      message: 'تم الاتصال بنجاح'
    };

  } catch (error) {
    console.error('❌ خطأ في الشبكة:', error);
    return {
      success: false,
      error: error.message,
      details: 'خطأ في الشبكة أو CORS'
    };
  }
};

/**
 * اختبار جميع الجداول
 */
export const testAllTables = async () => {
  console.log('🔄 اختبار جميع الجداول...');
  
  const tables = ['users', 'courses', 'faqs', 'settings', 'enrollments'];
  const results = [];

  for (const table of tables) {
    try {
      const { data, error } = await supabaseClient
        .from(table)
        .select('*')
        .limit(1);

      results.push({
        table: table,
        success: !error,
        count: data?.length || 0,
        error: error?.message
      });

      if (error) {
        console.warn(`⚠️ مشكلة في جدول ${table}:`, error.message);
      } else {
        console.log(`✅ جدول ${table}: ${data?.length || 0} سجل`);
      }

    } catch (error) {
      results.push({
        table: table,
        success: false,
        count: 0,
        error: error.message
      });
      console.error(`❌ خطأ في جدول ${table}:`, error.message);
    }
  }

  return results;
};

/**
 * إصلاح مشاكل CORS
 */
export const fixCORSIssues = async () => {
  console.log('🔧 محاولة إصلاح مشاكل CORS...');
  
  try {
    // اختبار مع headers مختلفة
    const response = await fetch(`${SUPABASE_URL}/rest/v1/settings?select=key,value&limit=1`, {
      method: 'GET',
      headers: {
        'apikey': SUPABASE_ANON_KEY,
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
        'Content-Type': 'application/json',
        'Prefer': 'return=minimal'
      }
    });

    if (response.ok) {
      const data = await response.json();
      console.log('✅ تم إصلاح مشكلة CORS');
      return { success: true, data };
    } else {
      console.error('❌ فشل في إصلاح CORS:', response.status);
      return { success: false, error: `HTTP ${response.status}` };
    }

  } catch (error) {
    console.error('❌ خطأ في إصلاح CORS:', error);
    return { success: false, error: error.message };
  }
};

/**
 * تشخيص شامل للمشاكل
 */
export const diagnoseProblem = async () => {
  console.log('🔍 بدء التشخيص الشامل...');
  
  const diagnosis = {
    timestamp: new Date().toISOString(),
    tests: []
  };

  // اختبار 1: الاتصال المباشر
  console.log('1️⃣ اختبار الاتصال المباشر...');
  const directTest = await testDirectConnection();
  diagnosis.tests.push({
    name: 'Direct Connection',
    ...directTest
  });

  // اختبار 2: CORS
  console.log('2️⃣ اختبار CORS...');
  const corsTest = await fixCORSIssues();
  diagnosis.tests.push({
    name: 'CORS Test',
    ...corsTest
  });

  // اختبار 3: الجداول
  console.log('3️⃣ اختبار الجداول...');
  const tablesTest = await testAllTables();
  diagnosis.tests.push({
    name: 'Tables Test',
    success: tablesTest.every(t => t.success),
    data: tablesTest
  });

  // النتيجة النهائية
  const successfulTests = diagnosis.tests.filter(t => t.success).length;
  const totalTests = diagnosis.tests.length;
  
  diagnosis.summary = {
    total: totalTests,
    successful: successfulTests,
    failed: totalTests - successfulTests,
    successRate: ((successfulTests / totalTests) * 100).toFixed(1) + '%'
  };

  console.log('📊 نتائج التشخيص:', diagnosis.summary);
  
  if (successfulTests === totalTests) {
    console.log('🎉 جميع الاختبارات نجحت! Supabase يعمل بشكل صحيح');
  } else {
    console.warn('⚠️ بعض الاختبارات فشلت. راجع التفاصيل أعلاه');
  }

  return diagnosis;
};

/**
 * إعادة تهيئة الاتصال
 */
export const reinitializeConnection = async () => {
  console.log('🔄 إعادة تهيئة اتصال Supabase...');
  
  try {
    // إنشاء عميل جديد
    const newClient = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
      auth: {
        persistSession: true,
        autoRefreshToken: true
      },
      realtime: {
        params: {
          eventsPerSecond: 10
        }
      }
    });

    // اختبار العميل الجديد
    const { data, error } = await newClient
      .from('settings')
      .select('key, value')
      .eq('key', 'academy_name')
      .single();

    if (error) {
      console.error('❌ فشل في إعادة التهيئة:', error);
      return { success: false, error: error.message };
    }

    console.log('✅ تم إعادة تهيئة الاتصال بنجاح');
    console.log('🏫 اسم الأكاديمية:', data?.value);
    
    return { 
      success: true, 
      client: newClient,
      data: data 
    };

  } catch (error) {
    console.error('❌ خطأ في إعادة التهيئة:', error);
    return { success: false, error: error.message };
  }
};

/**
 * إصلاح سريع لجميع المشاكل
 */
export const quickFix = async () => {
  console.log('⚡ بدء الإصلاح السريع...');
  
  // 1. تشخيص المشكلة
  const diagnosis = await diagnoseProblem();
  
  // 2. إعادة تهيئة الاتصال
  const reinit = await reinitializeConnection();
  
  // 3. اختبار نهائي
  const finalTest = await testDirectConnection();
  
  const result = {
    diagnosis,
    reinitialize: reinit,
    finalTest,
    fixed: finalTest.success
  };

  if (result.fixed) {
    console.log('🎉 تم إصلاح المشكلة بنجاح!');
  } else {
    console.error('❌ لم يتم إصلاح المشكلة. يرجى المراجعة اليدوية');
  }

  return result;
};

// تشغيل اختبار تلقائي عند تحميل الملف
if (typeof window !== 'undefined') {
  console.log('🚀 تحميل ملف إصلاح Supabase...');
  
  // تشغيل اختبار بعد 3 ثواني
  setTimeout(async () => {
    console.log('🔄 تشغيل اختبار تلقائي...');
    const result = await testDirectConnection();
    
    if (result.success) {
      console.log('✅ Supabase يعمل بشكل صحيح!');
      window.supabaseStatus = 'connected';
    } else {
      console.warn('⚠️ مشكلة في Supabase:', result.error);
      window.supabaseStatus = 'error';
      
      // محاولة إصلاح تلقائي
      console.log('🔧 محاولة إصلاح تلقائي...');
      const fixResult = await quickFix();
      
      if (fixResult.fixed) {
        console.log('🎉 تم الإصلاح التلقائي!');
        window.supabaseStatus = 'fixed';
      }
    }
  }, 3000);
}

export default {
  testDirectConnection,
  testAllTables,
  fixCORSIssues,
  diagnoseProblem,
  reinitializeConnection,
  quickFix,
  supabaseClient
};
