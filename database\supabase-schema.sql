-- قاعدة بيانات SKILLS WORLD ACADEMY - Supabase PostgreSQL
-- Skills World Academy Database Schema for Supabase
-- تم تحسينها للأداء العالي والمزامنة الفورية

-- تفعيل الامتدادات المطلوبة
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- جدول المستخدمين (المدراء والطلاب)
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    firebase_uid TEXT UNIQUE, -- ربط مع Firebase Auth
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE,
    phone VARCHAR(20),
    role VARCHAR(20) NOT NULL CHECK (role IN ('admin', 'student')),
    student_code VARCHAR(6) UNIQUE,
    avatar_url TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    enrolled_courses INTEGER DEFAULT 0,
    total_progress DECIMAL(5,2) DEFAULT 0,
    certificates_earned INTEGER DEFAULT 0,
    last_login TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- جدول الكورسات
CREATE TABLE courses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    instructor VARCHAR(255) NOT NULL DEFAULT 'علاء عبد الحميد',
    duration VARCHAR(50),
    level VARCHAR(20) DEFAULT 'مبتدئ' CHECK (level IN ('مبتدئ', 'متوسط', 'متقدم')),
    price DECIMAL(10,2) DEFAULT 0.00,
    thumbnail_url TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    enrolled_students INTEGER DEFAULT 0,
    total_videos INTEGER DEFAULT 0,
    tags TEXT[],
    category VARCHAR(100),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- جدول فيديوهات الكورسات
CREATE TABLE course_videos (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    course_id UUID NOT NULL REFERENCES courses(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    video_url TEXT NOT NULL,
    duration INTEGER, -- بالثواني
    order_index INTEGER NOT NULL,
    is_free BOOLEAN DEFAULT FALSE,
    thumbnail_url TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- جدول التسجيلات
CREATE TABLE enrollments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    student_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    course_id UUID NOT NULL REFERENCES courses(id) ON DELETE CASCADE,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'completed', 'suspended')),
    progress DECIMAL(5,2) DEFAULT 0,
    completed_videos UUID[],
    start_date TIMESTAMPTZ DEFAULT NOW(),
    completion_date TIMESTAMPTZ,
    last_accessed_at TIMESTAMPTZ DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(student_id, course_id)
);

-- جدول الأسئلة الشائعة
CREATE TABLE faqs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    question TEXT NOT NULL,
    answer TEXT NOT NULL,
    category VARCHAR(100),
    priority INTEGER DEFAULT 1,
    is_active BOOLEAN DEFAULT TRUE,
    views INTEGER DEFAULT 0,
    helpful_count INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- جدول الشهادات
CREATE TABLE certificates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    student_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    course_id UUID NOT NULL REFERENCES courses(id) ON DELETE CASCADE,
    certificate_number VARCHAR(50) UNIQUE NOT NULL,
    issue_date TIMESTAMPTZ DEFAULT NOW(),
    certificate_url TEXT,
    is_valid BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- جدول الإشعارات
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type VARCHAR(50),
    data JSONB,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- جدول رسائل الدردشة
CREATE TABLE chat_messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    sender_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    receiver_id UUID REFERENCES users(id) ON DELETE CASCADE,
    message TEXT NOT NULL,
    message_type VARCHAR(20) DEFAULT 'text' CHECK (message_type IN ('text', 'image', 'file')),
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- جدول الإعدادات العامة
CREATE TABLE settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type VARCHAR(20) DEFAULT 'string',
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- إنشاء الفهارس للأداء العالي
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_student_code ON users(student_code) WHERE student_code IS NOT NULL;
CREATE INDEX idx_users_firebase_uid ON users(firebase_uid) WHERE firebase_uid IS NOT NULL;
CREATE INDEX idx_users_email ON users(email) WHERE email IS NOT NULL;
CREATE INDEX idx_users_active ON users(is_active);

CREATE INDEX idx_courses_active ON courses(is_active);
CREATE INDEX idx_courses_category ON courses(category) WHERE category IS NOT NULL;
CREATE INDEX idx_courses_instructor ON courses(instructor);

CREATE INDEX idx_course_videos_course_id ON course_videos(course_id);
CREATE INDEX idx_course_videos_order ON course_videos(course_id, order_index);

CREATE INDEX idx_enrollments_student_id ON enrollments(student_id);
CREATE INDEX idx_enrollments_course_id ON enrollments(course_id);
CREATE INDEX idx_enrollments_status ON enrollments(status);
CREATE INDEX idx_enrollments_student_course ON enrollments(student_id, course_id);

CREATE INDEX idx_faqs_active ON faqs(is_active);
CREATE INDEX idx_faqs_category ON faqs(category) WHERE category IS NOT NULL;

CREATE INDEX idx_certificates_student_id ON certificates(student_id);
CREATE INDEX idx_certificates_course_id ON certificates(course_id);
CREATE INDEX idx_certificates_number ON certificates(certificate_number);

CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_unread ON notifications(user_id, is_read) WHERE is_read = FALSE;

CREATE INDEX idx_chat_messages_sender ON chat_messages(sender_id);
CREATE INDEX idx_chat_messages_receiver ON chat_messages(receiver_id);
CREATE INDEX idx_chat_messages_conversation ON chat_messages(sender_id, receiver_id, created_at);

-- إنشاء دوال التحديث التلقائي للـ updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- تطبيق دوال التحديث على الجداول
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_courses_updated_at BEFORE UPDATE ON courses FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_course_videos_updated_at BEFORE UPDATE ON course_videos FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_enrollments_updated_at BEFORE UPDATE ON enrollments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_faqs_updated_at BEFORE UPDATE ON faqs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_settings_updated_at BEFORE UPDATE ON settings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- تفعيل Row Level Security (RLS)
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE courses ENABLE ROW LEVEL SECURITY;
ALTER TABLE course_videos ENABLE ROW LEVEL SECURITY;
ALTER TABLE enrollments ENABLE ROW LEVEL SECURITY;
ALTER TABLE faqs ENABLE ROW LEVEL SECURITY;
ALTER TABLE certificates ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_messages ENABLE ROW LEVEL SECURITY;

-- سياسات الأمان (Security Policies)
-- المدراء يمكنهم الوصول لكل شيء
CREATE POLICY "Admins can access all data" ON users FOR ALL USING (
    EXISTS (
        SELECT 1 FROM users u 
        WHERE u.firebase_uid = auth.uid() 
        AND u.role = 'admin'
    )
);

-- الطلاب يمكنهم الوصول لبياناتهم فقط
CREATE POLICY "Students can access their own data" ON users FOR SELECT USING (
    firebase_uid = auth.uid()
);

-- الكورسات متاحة للقراءة للجميع
CREATE POLICY "Courses are readable by all authenticated users" ON courses FOR SELECT USING (
    auth.role() = 'authenticated'
);

-- الأسئلة الشائعة متاحة للقراءة للجميع
CREATE POLICY "FAQs are readable by all authenticated users" ON faqs FOR SELECT USING (
    is_active = true
);

-- إدراج البيانات الأساسية
INSERT INTO settings (setting_key, setting_value, setting_type, description, is_public) VALUES
('academy_name', 'SKILLS WORLD ACADEMY', 'string', 'اسم الأكاديمية', TRUE),
('admin_name', 'علاء عبد الحميد', 'string', 'اسم المدير', TRUE),
('admin_display_name', 'ALAA ABD HAMIED', 'string', 'الاسم المعروض للمدير', TRUE),
('admin_email', 'ALAA <EMAIL>', 'string', 'بريد المدير', FALSE),
('admin_phone', '0506747770', 'string', 'هاتف المدير', FALSE),
('academy_version', '3.0.0', 'string', 'إصدار النظام', TRUE),
('database_type', 'supabase-postgresql', 'string', 'نوع قاعدة البيانات', FALSE),
('firebase_project', 'marketwise-academy-qhizq', 'string', 'معرف مشروع Firebase', FALSE),
('website_url', 'https://marketwise-academy-qhizq.web.app', 'string', 'رابط الموقع', TRUE),
('max_students_per_course', '1000', 'number', 'الحد الأقصى للطلاب في الكورس', FALSE),
('enable_realtime_sync', 'true', 'boolean', 'تفعيل المزامنة الفورية', FALSE);

-- إنشاء المدير الرئيسي (سيتم ربطه مع Firebase Auth لاحقاً)
INSERT INTO users (name, email, phone, role, is_active) VALUES
('علاء عبد الحميد', 'ALAA <EMAIL>', '0506747770', 'admin', TRUE);

COMMENT ON TABLE users IS 'جدول المستخدمين - المدراء والطلاب';
COMMENT ON TABLE courses IS 'جدول الكورسات التعليمية';
COMMENT ON TABLE course_videos IS 'جدول فيديوهات الكورسات';
COMMENT ON TABLE enrollments IS 'جدول تسجيلات الطلاب في الكورسات';
COMMENT ON TABLE faqs IS 'جدول الأسئلة الشائعة';
COMMENT ON TABLE certificates IS 'جدول الشهادات';
COMMENT ON TABLE notifications IS 'جدول الإشعارات';
COMMENT ON TABLE chat_messages IS 'جدول رسائل الدردشة';
COMMENT ON TABLE settings IS 'جدول الإعدادات العامة للنظام';
