import { collection, addDoc, getDocs, query, where, serverTimestamp } from 'firebase/firestore';
import { db } from '../firebase/config';

/**
 * تهيئة بيانات تجريبية للاختبار
 */
export const initTestData = async () => {
  try {
    console.log('🔧 تهيئة البيانات التجريبية...');

    // التحقق من وجود المدير
    const adminQuery = query(
      collection(db, 'users'),
      where('role', '==', 'admin')
    );
    const adminSnapshot = await getDocs(adminQuery);

    if (adminSnapshot.empty) {
      // إنشاء حساب المدير
      await addDoc(collection(db, 'users'), {
        name: 'علاء عبد الحميد',
        email: '<EMAIL>',
        phone: '0506747770',
        role: 'admin',
        isActive: true,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });
      console.log('👨‍💼 تم إنشاء حساب المدير');
    } else {
      console.log('👨‍💼 حساب المدير موجود مسبقاً');
    }

    // التحقق من وجود طلاب
    const studentsQuery = query(
      collection(db, 'users'),
      where('role', '==', 'student')
    );
    const studentsSnapshot = await getDocs(studentsQuery);

    if (studentsSnapshot.empty) {
      // إنشاء طلاب تجريبيين
      const testStudents = [
        {
          name: 'أحمد محمد',
          email: '<EMAIL>',
          phone: '1234567890',
          studentCode: '123456',
          role: 'student',
          isActive: true,
          enrolledCourses: 0,
          completedCourses: 0,
          totalWatchTime: 0,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp()
        },
        {
          name: 'فاطمة علي',
          email: '<EMAIL>',
          phone: '0987654321',
          studentCode: '654321',
          role: 'student',
          isActive: true,
          enrolledCourses: 0,
          completedCourses: 0,
          totalWatchTime: 0,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp()
        },
        {
          name: 'محمد حسن',
          email: '<EMAIL>',
          phone: '5555555555',
          studentCode: '111111',
          role: 'student',
          isActive: true,
          enrolledCourses: 0,
          completedCourses: 0,
          totalWatchTime: 0,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp()
        }
      ];

      for (const student of testStudents) {
        await addDoc(collection(db, 'users'), student);
        console.log(`👨‍🎓 تم إنشاء الطالب: ${student.name} - كود: ${student.studentCode}`);
      }
    } else {
      console.log(`👨‍🎓 يوجد ${studentsSnapshot.size} طالب في قاعدة البيانات`);
    }

    console.log('✅ تم الانتهاء من تهيئة البيانات التجريبية');
    
    // عرض ملخص البيانات
    const allUsersSnapshot = await getDocs(collection(db, 'users'));
    const users = allUsersSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    
    const admins = users.filter(user => user.role === 'admin');
    const students = users.filter(user => user.role === 'student');
    
    console.log('📊 ملخص البيانات:');
    console.log(`👨‍💼 المدراء: ${admins.length}`);
    console.log(`👨‍🎓 الطلاب: ${students.length}`);
    
    if (students.length > 0) {
      console.log('📋 أكواد الطلاب المتاحة:');
      students.forEach(student => {
        console.log(`- ${student.name}: ${student.studentCode}`);
      });
    }
    
    return { admins, students };
    
  } catch (error) {
    console.error('❌ خطأ في تهيئة البيانات التجريبية:', error);
    throw error;
  }
};

/**
 * حذف جميع البيانات التجريبية
 */
export const clearTestData = async () => {
  try {
    console.log('🗑️ حذف البيانات التجريبية...');
    
    const usersSnapshot = await getDocs(collection(db, 'users'));
    
    for (const doc of usersSnapshot.docs) {
      await doc.ref.delete();
    }
    
    console.log('✅ تم حذف جميع البيانات');
    
  } catch (error) {
    console.error('❌ خطأ في حذف البيانات:', error);
    throw error;
  }
};

/**
 * إنشاء طالب جديد بكود مخصص
 */
export const createStudentWithCode = async (name, code) => {
  try {
    console.log(`👨‍🎓 إنشاء طالب جديد: ${name} - كود: ${code}`);
    
    // التحقق من عدم وجود الكود مسبقاً
    const existingQuery = query(
      collection(db, 'users'),
      where('studentCode', '==', code)
    );
    const existingSnapshot = await getDocs(existingQuery);
    
    if (!existingSnapshot.empty) {
      throw new Error(`الكود ${code} موجود مسبقاً`);
    }
    
    const studentData = {
      name,
      studentCode: code,
      role: 'student',
      isActive: true,
      enrolledCourses: 0,
      completedCourses: 0,
      totalWatchTime: 0,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    };
    
    const docRef = await addDoc(collection(db, 'users'), studentData);
    
    console.log(`✅ تم إنشاء الطالب بنجاح - ID: ${docRef.id}`);
    
    return { id: docRef.id, ...studentData };
    
  } catch (error) {
    console.error('❌ خطأ في إنشاء الطالب:', error);
    throw error;
  }
};

// تصدير الدوال للاستخدام في وحدة التحكم
window.initTestData = initTestData;
window.clearTestData = clearTestData;
window.createStudentWithCode = createStudentWithCode;

export default {
  initTestData,
  clearTestData,
  createStudentWithCode
};
