import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  Grid,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  IconButton,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  CircularProgress,
  Alert,
  Avatar,
  LinearProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tooltip
} from '@mui/material';
import {
  Add,
  Delete,
  School,
  Person,
  Save,
  Cancel,
  CheckCircle,
  PlayCircle,
  TrendingUp,
  Assignment
} from '@mui/icons-material';
import toast from 'react-hot-toast';

// استيراد خدمة قاعدة البيانات الجديدة للإنتاج
import {
  enrollmentProductionService,
  studentProductionService,
  courseProductionService
} from '../../firebase/productionDatabaseService';

const StudentEnrollmentManagement = () => {
  const [enrollments, setEnrollments] = useState([]);
  const [students, setStudents] = useState([]);
  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [formData, setFormData] = useState({
    studentId: '',
    courseId: '',
    progress: 0,
    isCompleted: false
  });

  // جلب التسجيلات من قاعدة البيانات مع التحديث الفوري (بدون فهارس معقدة)
  useEffect(() => {
    console.log('🔄 بدء مراقبة التسجيلات...');

    const unsubscribe = onSnapshot(
      collection(db, 'enrollments'),
      (snapshot) => {
        const enrollmentsData = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          enrolledAt: doc.data().enrolledAt?.toDate?.() || new Date(),
          completedAt: doc.data().completedAt?.toDate?.() || null
        }));

        // ترتيب البيانات محلياً بدلاً من في الاستعلام
        enrollmentsData.sort((a, b) => b.enrolledAt - a.enrolledAt);

        setEnrollments(enrollmentsData);
        console.log('✅ تم تحديث التسجيلات:', enrollmentsData.length);
      },
      (error) => {
        console.error('❌ خطأ في جلب التسجيلات:', error);
        toast.error('فشل في جلب التسجيلات');
      }
    );

    return () => {
      console.log('🛑 إيقاف مراقبة التسجيلات');
      unsubscribe();
    };
  }, []);

  // جلب الطلاب
  useEffect(() => {
    const unsubscribe = onSnapshot(
      query(collection(db, 'users'), where('role', '==', 'student')),
      (snapshot) => {
        const studentsData = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
        setStudents(studentsData);
      },
      (error) => {
        console.error('❌ خطأ في جلب الطلاب:', error);
      }
    );

    return () => unsubscribe();
  }, []);

  // جلب الكورسات
  useEffect(() => {
    const unsubscribe = onSnapshot(
      query(collection(db, 'courses')),
      (snapshot) => {
        const coursesData = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
        setCourses(coursesData);
      },
      (error) => {
        console.error('❌ خطأ في جلب الكورسات:', error);
      }
    );

    return () => unsubscribe();
  }, []);

  // تسجيل طالب في كورس
  const handleEnrollStudent = async () => {
    if (!formData.studentId || !formData.courseId) {
      toast.error('يرجى اختيار الطالب والكورس');
      return;
    }

    // التحقق من عدم وجود تسجيل مسبق
    const existingEnrollment = enrollments.find(
      e => e.studentId === formData.studentId && e.courseId === formData.courseId
    );

    if (existingEnrollment) {
      toast.error('الطالب مسجل في هذا الكورس مسبقاً');
      return;
    }

    setLoading(true);
    try {
      const enrollmentData = {
        studentId: formData.studentId,
        courseId: formData.courseId,
        progress: 0,
        isCompleted: false,
        enrolledAt: serverTimestamp(),
        lastAccessed: serverTimestamp()
      };

      await addDoc(collection(db, 'enrollments'), enrollmentData);
      
      // تحديث عدد الطلاب المسجلين في الكورس
      const course = courses.find(c => c.id === formData.courseId);
      if (course) {
        const courseRef = doc(db, 'courses', formData.courseId);
        await updateDoc(courseRef, {
          enrolledStudents: (course.enrolledStudents || 0) + 1,
          updatedAt: serverTimestamp()
        });
      }

      // تحديث عدد الكورسات المسجل فيها الطالب
      const student = students.find(s => s.id === formData.studentId);
      if (student) {
        const studentRef = doc(db, 'users', formData.studentId);
        await updateDoc(studentRef, {
          enrolledCourses: (student.enrolledCourses || 0) + 1,
          updatedAt: serverTimestamp()
        });
      }
      
      console.log('✅ تم تسجيل الطالب في الكورس');
      toast.success('تم تسجيل الطالب في الكورس بنجاح');
      
      handleCloseDialog();
    } catch (error) {
      console.error('❌ خطأ في تسجيل الطالب:', error);
      toast.error('فشل في تسجيل الطالب');
    } finally {
      setLoading(false);
    }
  };

  // إلغاء تسجيل طالب من كورس
  const handleUnenrollStudent = async (enrollmentId, studentId, courseId) => {
    const student = students.find(s => s.id === studentId);
    const course = courses.find(c => c.id === courseId);
    
    if (!window.confirm(`هل أنت متأكد من إلغاء تسجيل "${student?.name}" من كورس "${course?.title}"؟`)) {
      return;
    }

    setLoading(true);
    try {
      await deleteDoc(doc(db, 'enrollments', enrollmentId));
      
      // تحديث عدد الطلاب المسجلين في الكورس
      if (course) {
        const courseRef = doc(db, 'courses', courseId);
        await updateDoc(courseRef, {
          enrolledStudents: Math.max((course.enrolledStudents || 1) - 1, 0),
          updatedAt: serverTimestamp()
        });
      }

      // تحديث عدد الكورسات المسجل فيها الطالب
      if (student) {
        const studentRef = doc(db, 'users', studentId);
        await updateDoc(studentRef, {
          enrolledCourses: Math.max((student.enrolledCourses || 1) - 1, 0),
          updatedAt: serverTimestamp()
        });
      }
      
      console.log('✅ تم إلغاء تسجيل الطالب');
      toast.success('تم إلغاء تسجيل الطالب بنجاح');
    } catch (error) {
      console.error('❌ خطأ في إلغاء التسجيل:', error);
      toast.error('فشل في إلغاء التسجيل');
    } finally {
      setLoading(false);
    }
  };

  // تحديث تقدم الطالب
  const handleUpdateProgress = async (enrollmentId, newProgress) => {
    try {
      const enrollmentRef = doc(db, 'enrollments', enrollmentId);
      const updateData = {
        progress: newProgress,
        isCompleted: newProgress >= 100,
        updatedAt: serverTimestamp()
      };

      if (newProgress >= 100) {
        updateData.completedAt = serverTimestamp();
      }

      await updateDoc(enrollmentRef, updateData);
      
      toast.success('تم تحديث التقدم بنجاح');
    } catch (error) {
      console.error('❌ خطأ في تحديث التقدم:', error);
      toast.error('فشل في تحديث التقدم');
    }
  };

  // فتح نافذة تسجيل جديد
  const handleOpenAddDialog = () => {
    setFormData({
      studentId: '',
      courseId: '',
      progress: 0,
      isCompleted: false
    });
    setOpenDialog(true);
  };

  // إغلاق النافذة
  const handleCloseDialog = () => {
    setOpenDialog(false);
    setFormData({
      studentId: '',
      courseId: '',
      progress: 0,
      isCompleted: false
    });
  };

  // تحديث بيانات النموذج
  const handleFormChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // تنسيق التاريخ
  const formatDate = (date) => {
    if (!date) return 'غير محدد';
    if (date.toDate) date = date.toDate();
    return new Intl.DateTimeFormat('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  // الحصول على اسم الطالب
  const getStudentName = (studentId) => {
    const student = students.find(s => s.id === studentId);
    return student ? student.name : 'طالب غير معروف';
  };

  // الحصول على عنوان الكورس
  const getCourseTitle = (courseId) => {
    const course = courses.find(c => c.id === courseId);
    return course ? course.title : 'كورس غير معروف';
  };

  // الحصول على كود الطالب
  const getStudentCode = (studentId) => {
    const student = students.find(s => s.id === studentId);
    return student ? student.studentCode : 'غير محدد';
  };

  return (
    <Box>
      {/* رأس الصفحة */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Assignment color="primary" />
          إدارة التسجيلات
        </Typography>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={handleOpenAddDialog}
          size="large"
          sx={{ borderRadius: 2 }}
        >
          تسجيل طالب في كورس
        </Button>
      </Box>

      {/* إحصائيات سريعة */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ textAlign: 'center', p: 2 }}>
            <Typography variant="h4" color="primary">{enrollments.length}</Typography>
            <Typography variant="body2" color="text.secondary">إجمالي التسجيلات</Typography>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ textAlign: 'center', p: 2 }}>
            <Typography variant="h4" color="success.main">
              {enrollments.filter(e => e.isCompleted).length}
            </Typography>
            <Typography variant="body2" color="text.secondary">الكورسات المكتملة</Typography>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ textAlign: 'center', p: 2 }}>
            <Typography variant="h4" color="info.main">
              {enrollments.filter(e => e.progress > 0 && !e.isCompleted).length}
            </Typography>
            <Typography variant="body2" color="text.secondary">قيد التقدم</Typography>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ textAlign: 'center', p: 2 }}>
            <Typography variant="h4" color="warning.main">
              {enrollments.length > 0 ? Math.round(
                enrollments.reduce((sum, e) => sum + (e.progress || 0), 0) / enrollments.length
              ) : 0}%
            </Typography>
            <Typography variant="body2" color="text.secondary">متوسط التقدم</Typography>
          </Card>
        </Grid>
      </Grid>

      {/* جدول التسجيلات */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            قائمة التسجيلات ({enrollments.length})
          </Typography>
          
          {loading && (
            <Box display="flex" justifyContent="center" p={3}>
              <CircularProgress />
            </Box>
          )}

          {!loading && enrollments.length === 0 && (
            <Alert severity="info" sx={{ mt: 2 }}>
              لا توجد تسجيلات حالياً. اضغط على "تسجيل طالب في كورس" لإنشاء أول تسجيل.
            </Alert>
          )}

          {!loading && enrollments.length > 0 && (
            <TableContainer component={Paper} sx={{ mt: 2 }}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>الطالب</TableCell>
                    <TableCell>الكورس</TableCell>
                    <TableCell>التقدم</TableCell>
                    <TableCell>الحالة</TableCell>
                    <TableCell>تاريخ التسجيل</TableCell>
                    <TableCell>تاريخ الإكمال</TableCell>
                    <TableCell>الإجراءات</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {enrollments.map((enrollment) => (
                    <TableRow key={enrollment.id}>
                      <TableCell>
                        <Box display="flex" alignItems="center" gap={2}>
                          <Avatar sx={{ bgcolor: 'primary.main' }}>
                            <Person />
                          </Avatar>
                          <Box>
                            <Typography variant="subtitle2" fontWeight="bold">
                              {getStudentName(enrollment.studentId)}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              كود: {getStudentCode(enrollment.studentId)}
                            </Typography>
                          </Box>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Box>
                          <Typography variant="subtitle2" fontWeight="bold">
                            {getCourseTitle(enrollment.courseId)}
                          </Typography>
                          <Chip 
                            icon={<School />}
                            label="كورس"
                            size="small"
                            variant="outlined"
                          />
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Box sx={{ width: '100%' }}>
                          <Box display="flex" alignItems="center" gap={1} mb={1}>
                            <Typography variant="body2">
                              {enrollment.progress || 0}%
                            </Typography>
                            <TrendingUp 
                              fontSize="small" 
                              color={enrollment.progress >= 50 ? 'success' : 'action'}
                            />
                          </Box>
                          <LinearProgress 
                            variant="determinate" 
                            value={enrollment.progress || 0}
                            sx={{ height: 8, borderRadius: 4 }}
                            color={
                              enrollment.progress >= 100 ? 'success' :
                              enrollment.progress >= 50 ? 'primary' : 'warning'
                            }
                          />
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip 
                          icon={enrollment.isCompleted ? <CheckCircle /> : <PlayCircle />}
                          label={enrollment.isCompleted ? 'مكتمل' : 'قيد التقدم'}
                          size="small"
                          color={enrollment.isCompleted ? 'success' : 'primary'}
                          variant={enrollment.isCompleted ? 'filled' : 'outlined'}
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="caption">
                          {formatDate(enrollment.enrolledAt)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="caption">
                          {enrollment.completedAt ? formatDate(enrollment.completedAt) : 'لم يكتمل بعد'}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Box display="flex" gap={1}>
                          <Tooltip title="إلغاء التسجيل">
                            <IconButton
                              size="small"
                              onClick={() => handleUnenrollStudent(
                                enrollment.id, 
                                enrollment.studentId, 
                                enrollment.courseId
                              )}
                              color="error"
                            >
                              <Delete />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </CardContent>
      </Card>

      {/* نافذة تسجيل طالب في كورس */}
      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
        maxWidth="sm"
        fullWidth
        aria-labelledby="enrollment-dialog-title"
        aria-describedby="enrollment-dialog-description"
      >
        <DialogTitle id="enrollment-dialog-title">
          تسجيل طالب في كورس
        </DialogTitle>
        <DialogContent id="enrollment-dialog-description">
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>اختر الطالب</InputLabel>
                <Select
                  value={formData.studentId}
                  onChange={(e) => handleFormChange('studentId', e.target.value)}
                  label="اختر الطالب"
                >
                  {students.filter(s => s.isActive).map((student) => (
                    <MenuItem key={student.id} value={student.id}>
                      {student.name} - كود: {student.studentCode}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>اختر الكورس</InputLabel>
                <Select
                  value={formData.courseId}
                  onChange={(e) => handleFormChange('courseId', e.target.value)}
                  label="اختر الكورس"
                >
                  {courses.filter(c => c.isActive).map((course) => (
                    <MenuItem key={course.id} value={course.id}>
                      {course.title} - {course.instructor}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog} startIcon={<Cancel />}>
            إلغاء
          </Button>
          <Button
            onClick={handleEnrollStudent}
            variant="contained"
            startIcon={<Save />}
            disabled={loading}
          >
            {loading ? <CircularProgress size={20} /> : 'تسجيل'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default StudentEnrollmentManagement;
