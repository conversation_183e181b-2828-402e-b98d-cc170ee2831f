import { createClient } from '@supabase/supabase-js';

/**
 * إعداد Supabase للأكاديمية
 * Skills World Academy - Supabase Configuration
 */

// إعدادات Supabase من متغيرات البيئة مع قيم احتياطية
const supabaseUrl = process.env.REACT_APP_SUPABASE_URL || 'https://auwpeiicfwcysoexoogf.supabase.co';
const supabaseAnonKey = process.env.REACT_APP_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImF1d3BlaWljZndjeXNvZXhvb2dmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIxNzA3MzAsImV4cCI6MjA2Nzc0NjczMH0.3twZ1c6M2EyBoBe66PjmwsUwvzK0nnV89Bt3yLcGBjY';

// التحقق من وجود المتغيرات المطلوبة
if (!supabaseUrl || !supabaseAnonKey || supabaseUrl.includes('your-project')) {
  console.warn('⚠️ استخدام إعدادات Supabase الافتراضية');
  console.log('للحصول على أفضل أداء، يرجى تحديث ملف .env بالمتغيرات الصحيحة');
} else {
  console.log('✅ تم تحميل إعدادات Supabase بنجاح');
}

// إنشاء عميل Supabase مع إعدادات محسنة
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    // تكامل مع Firebase Auth
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  },
  realtime: {
    // إعدادات المزامنة الفورية
    params: {
      eventsPerSecond: 10
    }
  },
  db: {
    // إعدادات قاعدة البيانات
    schema: 'public'
  }
});

// دالة اختبار الاتصال
export const testSupabaseConnection = async () => {
  try {
    console.log('🔄 اختبار الاتصال مع Supabase...');
    
    const { data, error } = await supabase
      .from('settings')
      .select('key, value')
      .eq('key', 'academy_name')
      .single();

    if (error) {
      console.error('❌ خطأ في الاتصال مع Supabase:', error);
      return false;
    }

    console.log('✅ تم الاتصال بنجاح مع Supabase');
    console.log('🏫 اسم الأكاديمية:', data?.value);
    return true;
  } catch (error) {
    console.error('❌ خطأ في اختبار الاتصال:', error);
    return false;
  }
};

// دالة تهيئة Supabase
export const initializeSupabase = async () => {
  try {
    console.log('🚀 تهيئة Supabase...');
    
    // اختبار الاتصال
    const isConnected = await testSupabaseConnection();
    
    if (!isConnected) {
      throw new Error('فشل في الاتصال مع Supabase');
    }

    // إعداد مستمعات المزامنة الفورية
    setupRealtimeListeners();
    
    console.log('✅ تم تهيئة Supabase بنجاح');
    return true;
  } catch (error) {
    console.error('❌ خطأ في تهيئة Supabase:', error);
    throw error;
  }
};

// إعداد مستمعات المزامنة الفورية
const setupRealtimeListeners = () => {
  console.log('🔄 إعداد مستمعات المزامنة الفورية...');
  
  // مستمع تغييرات الكورسات
  supabase
    .channel('courses_changes')
    .on('postgres_changes', 
      { event: '*', schema: 'public', table: 'courses' },
      (payload) => {
        console.log('📚 تغيير في الكورسات:', payload);
        // سيتم التعامل مع هذا في الخدمات المختصة
      }
    )
    .subscribe();

  // مستمع تغييرات الطلاب
  supabase
    .channel('users_changes')
    .on('postgres_changes', 
      { event: '*', schema: 'public', table: 'users', filter: 'role=eq.student' },
      (payload) => {
        console.log('👨‍🎓 تغيير في الطلاب:', payload);
      }
    )
    .subscribe();

  // مستمع تغييرات التسجيلات
  supabase
    .channel('enrollments_changes')
    .on('postgres_changes', 
      { event: '*', schema: 'public', table: 'enrollments' },
      (payload) => {
        console.log('📝 تغيير في التسجيلات:', payload);
      }
    )
    .subscribe();

  // مستمع تغييرات الأسئلة الشائعة
  supabase
    .channel('faqs_changes')
    .on('postgres_changes', 
      { event: '*', schema: 'public', table: 'faqs' },
      (payload) => {
        console.log('❓ تغيير في الأسئلة الشائعة:', payload);
      }
    )
    .subscribe();

  console.log('✅ تم إعداد مستمعات المزامنة الفورية');
};

// دالة تنظيف الاتصالات
export const cleanupSupabase = () => {
  console.log('🧹 تنظيف اتصالات Supabase...');
  supabase.removeAllChannels();
};

// تصدير العميل الافتراضي
export default supabase;
