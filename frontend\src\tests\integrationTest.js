/**
 * اختبار التكامل الشامل - Firebase + Supabase
 * Skills World Academy Integration Test Suite
 */

import { 
  hybridAuth,
  hybridCourses,
  hybridStudents,
  hybridEnrollments,
  hybridFAQs 
} from '../services/hybridDatabaseService';
import { initializeSupabase, testSupabaseConnection } from '../supabase/config';

/**
 * مجموعة اختبارات التكامل
 */
export class IntegrationTestSuite {
  constructor() {
    this.testResults = [];
    this.isRunning = false;
  }

  // تشغيل جميع الاختبارات
  async runAllTests() {
    if (this.isRunning) {
      console.log('⚠️ الاختبارات قيد التشغيل بالفعل');
      return;
    }

    this.isRunning = true;
    this.testResults = [];
    
    console.log('🚀 بدء اختبار التكامل الشامل...');
    console.log('=' .repeat(50));

    try {
      // اختبار الاتصال الأساسي
      await this.testBasicConnections();
      
      // اختبار المصادقة
      await this.testAuthentication();
      
      // اختبار إدارة الكورسات
      await this.testCourseManagement();
      
      // اختبار إدارة الطلاب
      await this.testStudentManagement();
      
      // اختبار التسجيلات
      await this.testEnrollmentManagement();
      
      // اختبار الأسئلة الشائعة
      await this.testFAQManagement();
      
      // اختبار المزامنة الفورية
      await this.testRealtimeSync();
      
      // اختبار الأداء
      await this.testPerformance();

      // عرض النتائج النهائية
      this.displayResults();
      
    } catch (error) {
      console.error('❌ خطأ في تشغيل الاختبارات:', error);
    } finally {
      this.isRunning = false;
    }
  }

  // اختبار الاتصالات الأساسية
  async testBasicConnections() {
    console.log('🔗 اختبار الاتصالات الأساسية...');
    
    try {
      // اختبار Supabase
      const supabaseConnected = await testSupabaseConnection();
      this.addResult('Supabase Connection', supabaseConnected);
      
      // اختبار Firebase Auth
      const firebaseUser = hybridAuth.getCurrentUser();
      this.addResult('Firebase Auth Status', firebaseUser !== null);
      
      console.log('✅ اختبار الاتصالات مكتمل');
    } catch (error) {
      console.error('❌ خطأ في اختبار الاتصالات:', error);
      this.addResult('Basic Connections', false, error.message);
    }
  }

  // اختبار المصادقة
  async testAuthentication() {
    console.log('🔐 اختبار نظام المصادقة...');
    
    try {
      // اختبار الحصول على بيانات المستخدم
      const userData = await hybridAuth.getUserData();
      this.addResult('User Data Retrieval', userData !== null);
      
      if (userData) {
        console.log('👤 بيانات المستخدم:', userData.name, userData.role);
      }
      
      console.log('✅ اختبار المصادقة مكتمل');
    } catch (error) {
      console.error('❌ خطأ في اختبار المصادقة:', error);
      this.addResult('Authentication', false, error.message);
    }
  }

  // اختبار إدارة الكورسات
  async testCourseManagement() {
    console.log('📚 اختبار إدارة الكورسات...');
    
    try {
      // جلب الكورسات
      const courses = await hybridCourses.getAllCourses();
      this.addResult('Fetch Courses', Array.isArray(courses));
      
      console.log(`📊 عدد الكورسات: ${courses.length}`);
      
      // اختبار إضافة كورس تجريبي (إذا كان المستخدم مدير)
      const currentUser = await hybridAuth.getUserData();
      if (currentUser && currentUser.role === 'admin') {
        const testCourse = {
          title: 'كورس اختبار التكامل',
          description: 'كورس تجريبي لاختبار النظام',
          category: 'اختبار',
          level: 'مبتدئ',
          duration: '1 ساعة'
        };
        
        const addResult = await hybridCourses.addCourse(testCourse);
        this.addResult('Add Test Course', addResult.success);
        
        if (addResult.success) {
          // حذف الكورس التجريبي
          await hybridCourses.deleteCourse(addResult.id);
          console.log('🗑️ تم حذف الكورس التجريبي');
        }
      }
      
      console.log('✅ اختبار إدارة الكورسات مكتمل');
    } catch (error) {
      console.error('❌ خطأ في اختبار الكورسات:', error);
      this.addResult('Course Management', false, error.message);
    }
  }

  // اختبار إدارة الطلاب
  async testStudentManagement() {
    console.log('👨‍🎓 اختبار إدارة الطلاب...');
    
    try {
      // جلب الطلاب
      const students = await hybridStudents.getAllStudents();
      this.addResult('Fetch Students', Array.isArray(students));
      
      console.log(`📊 عدد الطلاب: ${students.length}`);
      
      console.log('✅ اختبار إدارة الطلاب مكتمل');
    } catch (error) {
      console.error('❌ خطأ في اختبار الطلاب:', error);
      this.addResult('Student Management', false, error.message);
    }
  }

  // اختبار إدارة التسجيلات
  async testEnrollmentManagement() {
    console.log('📝 اختبار إدارة التسجيلات...');
    
    try {
      // جلب التسجيلات
      const enrollments = await hybridEnrollments.getAllEnrollments();
      this.addResult('Fetch Enrollments', Array.isArray(enrollments));
      
      console.log(`📊 عدد التسجيلات: ${enrollments.length}`);
      
      console.log('✅ اختبار إدارة التسجيلات مكتمل');
    } catch (error) {
      console.error('❌ خطأ في اختبار التسجيلات:', error);
      this.addResult('Enrollment Management', false, error.message);
    }
  }

  // اختبار إدارة الأسئلة الشائعة
  async testFAQManagement() {
    console.log('❓ اختبار إدارة الأسئلة الشائعة...');
    
    try {
      // جلب الأسئلة الشائعة
      const faqs = await hybridFAQs.getAllFAQs();
      this.addResult('Fetch FAQs', Array.isArray(faqs));
      
      console.log(`📊 عدد الأسئلة الشائعة: ${faqs.length}`);
      
      console.log('✅ اختبار إدارة الأسئلة الشائعة مكتمل');
    } catch (error) {
      console.error('❌ خطأ في اختبار الأسئلة الشائعة:', error);
      this.addResult('FAQ Management', false, error.message);
    }
  }

  // اختبار المزامنة الفورية
  async testRealtimeSync() {
    console.log('🔄 اختبار المزامنة الفورية...');
    
    try {
      let realtimeWorking = false;
      
      // اختبار مراقبة الكورسات
      const unsubscribe = hybridCourses.watchCourses((courses) => {
        console.log('🔄 تحديث فوري للكورسات:', courses.length);
        realtimeWorking = true;
      });
      
      // انتظار قصير للتحقق من عمل المزامنة
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      unsubscribe();
      
      this.addResult('Realtime Sync', realtimeWorking);
      
      console.log('✅ اختبار المزامنة الفورية مكتمل');
    } catch (error) {
      console.error('❌ خطأ في اختبار المزامنة الفورية:', error);
      this.addResult('Realtime Sync', false, error.message);
    }
  }

  // اختبار الأداء
  async testPerformance() {
    console.log('⚡ اختبار الأداء...');
    
    try {
      // قياس سرعة جلب البيانات
      const startTime = performance.now();
      
      await Promise.all([
        hybridCourses.getAllCourses(),
        hybridStudents.getAllStudents(),
        hybridFAQs.getAllFAQs()
      ]);
      
      const endTime = performance.now();
      const loadTime = endTime - startTime;
      
      console.log(`⏱️ وقت تحميل البيانات: ${loadTime.toFixed(2)} مللي ثانية`);
      
      // الأداء جيد إذا كان أقل من 3 ثواني
      this.addResult('Performance Test', loadTime < 3000);
      
      console.log('✅ اختبار الأداء مكتمل');
    } catch (error) {
      console.error('❌ خطأ في اختبار الأداء:', error);
      this.addResult('Performance Test', false, error.message);
    }
  }

  // إضافة نتيجة اختبار
  addResult(testName, passed, error = null) {
    this.testResults.push({
      name: testName,
      passed,
      error,
      timestamp: new Date()
    });
  }

  // عرض النتائج النهائية
  displayResults() {
    console.log('\n' + '=' .repeat(50));
    console.log('📊 نتائج اختبار التكامل النهائية');
    console.log('=' .repeat(50));
    
    const passedTests = this.testResults.filter(test => test.passed).length;
    const totalTests = this.testResults.length;
    const successRate = ((passedTests / totalTests) * 100).toFixed(1);
    
    console.log(`✅ الاختبارات الناجحة: ${passedTests}/${totalTests} (${successRate}%)`);
    console.log('\nتفاصيل الاختبارات:');
    
    this.testResults.forEach(test => {
      const status = test.passed ? '✅' : '❌';
      console.log(`${status} ${test.name}`);
      if (!test.passed && test.error) {
        console.log(`   خطأ: ${test.error}`);
      }
    });
    
    console.log('\n' + '=' .repeat(50));
    
    if (successRate >= 90) {
      console.log('🎉 النظام جاهز للإنتاج!');
    } else if (successRate >= 70) {
      console.log('⚠️ النظام يحتاج بعض التحسينات');
    } else {
      console.log('🚨 النظام يحتاج مراجعة شاملة');
    }
  }

  // الحصول على النتائج
  getResults() {
    return {
      results: this.testResults,
      summary: {
        total: this.testResults.length,
        passed: this.testResults.filter(test => test.passed).length,
        failed: this.testResults.filter(test => !test.passed).length,
        successRate: ((this.testResults.filter(test => test.passed).length / this.testResults.length) * 100).toFixed(1)
      }
    };
  }
}

// إنشاء مثيل الاختبار
export const integrationTest = new IntegrationTestSuite();

// دالة سريعة لتشغيل الاختبارات
export const runQuickTest = async () => {
  console.log('🚀 تشغيل اختبار سريع...');
  await integrationTest.runAllTests();
  return integrationTest.getResults();
};

// تصدير للاستخدام في المكونات
export default integrationTest;
