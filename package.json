{"name": "alaa-abdulhameed-courses", "version": "1.0.0", "description": "منصة كورسات التسويق - علاء عبد الحميد", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "temp": "node temp-server.js", "client": "cd frontend && npm start", "server": "nodemon server.js", "build": "cd frontend && npm install && npm run build", "install-client": "cd frontend && npm install", "init-db": "node scripts/initDatabase.js", "docker:dev": "docker-compose up -d", "docker:prod": "docker-compose -f docker-compose.prod.yml up -d", "docker:build": "docker-compose build", "docker:stop": "docker-compose down", "deploy": "node scripts/deploy.js", "deploy:render": "git push origin main", "deploy:firebase": "firebase deploy", "firebase:init": "firebase init", "firebase:serve": "firebase serve", "test": "echo \"Error: no test specified\" && exit 1", "lint": "echo \"No linting configured\"", "postinstall": "npm run build", "heroku-postbuild": "npm run build"}, "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^6.10.0", "express-validator": "^7.0.1", "firebase": "^11.10.0", "helmet": "^7.0.0", "jsonwebtoken": "^9.0.2", "mongoose": "^7.5.0", "multer": "^1.4.5-lts.1", "pdfkit": "^0.13.0", "twilio": "^5.7.2", "uuid": "^9.0.0"}, "devDependencies": {"firebase-tools": "^12.9.1", "nodemon": "^3.0.1"}, "keywords": ["courses", "marketing", "education", "arabic"], "author": "علاء عبد الحميد", "license": "MIT"}